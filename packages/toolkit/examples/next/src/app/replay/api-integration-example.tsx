'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@cloc/ui';
import ClarityReplayEnhanced from './ClarityReplayEnhanced';
import { useClarityData } from './hooks/useClarityData';
import { trackingApiClient } from './api/tracking-client';
import { IClocSession } from './types/api-types';

interface ApiIntegrationExampleProps {
	defaultOrganizationId?: string;
	defaultTenantId?: string;
	defaultEmployeeId?: string;
}

/**
 * Complete example demonstrating the API-integrated Clarity replay system
 * Shows how to use the real tracking API endpoints with all enhanced features
 */
const ApiIntegrationExample: React.FC<ApiIntegrationExampleProps> = ({
	defaultOrganizationId = '',
	defaultTenantId = '',
	defaultEmployeeId = ''
}) => {
	// Form state for API context
	const [organizationId, setOrganizationId] = useState(defaultOrganizationId);
	const [tenantId, setTenantId] = useState(defaultTenantId);
	const [employeeId, setEmployeeId] = useState(defaultEmployeeId);
	const [selectedSessionId, setSelectedSessionId] = useState<string>('');

	// UI state
	const [activeTab, setActiveTab] = useState<'sessions' | 'replay'>('sessions');
	const [showApiConfig, setShowApiConfig] = useState(false);

	// Enhanced data management with full API integration
	const {
		sessions,
		decodedData,
		loading,
		error,
		progress,
		hasData,
		hasSessions,
		metadata,
		loadAllSessions,
		load,
		refetch,
		clearCache,
		getCacheInfo,
		apiClient
	} = useClarityData({
		organizationId,
		tenantId,
		employeeId,
		autoLoad: false, // Manual loading for this example
		enableCaching: true,
		retryAttempts: 3,
		retryDelay: 1000
	});

	// Load sessions when context changes
	const handleLoadSessions = async () => {
		if (!organizationId || !tenantId) {
			alert('Please provide Organization ID and Tenant ID');
			return;
		}
		await loadAllSessions();
	};

	// Load specific session for replay
	const handleLoadSession = async (sessionId: string) => {
		setSelectedSessionId(sessionId);
		await load(sessionId);
		setActiveTab('replay');
	};

	// Handle session selection
	const handleSessionSelect = (session: IClocSession) => {
		// Use timestamp as session identifier for this example
		handleLoadSession(session.timestamp);
	};

	// Store new session (example function)
	const handleStoreSession = async () => {
		if (!organizationId || !tenantId) {
			alert('Please provide Organization ID and Tenant ID');
			return;
		}

		try {
			const samplePayload = JSON.stringify({
				events: [
					{ time: 0, event: 'load', data: { url: 'https://example.com' } },
					{ time: 1000, event: 'click', data: { x: 100, y: 200 } },
					{ time: 2000, event: 'scroll', data: { scrollY: 500 } }
				]
			});

			const response = await apiClient.storeSessionWithContext(
				{
					payload: samplePayload,
					timestamp: new Date().toISOString()
				},
				organizationId,
				tenantId
			);

			if (response.success) {
				alert('Session stored successfully!');
				await loadAllSessions(); // Refresh the list
			} else {
				alert(`Failed to store session: ${response.error}`);
			}
		} catch (error) {
			console.error('Store session error:', error);
			alert('Failed to store session');
		}
	};

	// API configuration info
	const apiConfig = apiClient.getConfig();
	const cacheInfo = getCacheInfo();

	return (
		<div className="min-h-screen bg-gray-50 p-4">
			<div className="max-w-7xl mx-auto">
				{/* Header */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						Clarity API Integration Example
					</h1>
					<p className="text-gray-600">
						Complete demonstration of the enhanced Clarity replay system with real API integration
					</p>
				</div>

				{/* API Configuration Panel */}
				<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
					<div className="flex items-center justify-between mb-4">
						<h2 className="text-lg font-semibold">API Configuration</h2>
						<Button
							onClick={() => setShowApiConfig(!showApiConfig)}
							variant="outline"
							size="sm"
						>
							{showApiConfig ? 'Hide' : 'Show'} Config
						</Button>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Organization ID *
							</label>
							<input
								type="text"
								value={organizationId}
								onChange={(e) => setOrganizationId(e.target.value)}
								placeholder="Enter organization ID..."
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Tenant ID *
							</label>
							<input
								type="text"
								value={tenantId}
								onChange={(e) => setTenantId(e.target.value)}
								placeholder="Enter tenant ID..."
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Employee ID (Optional)
							</label>
							<input
								type="text"
								value={employeeId}
								onChange={(e) => setEmployeeId(e.target.value)}
								placeholder="Filter by employee..."
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
					</div>

					{showApiConfig && (
						<div className="mt-4 p-4 bg-gray-50 rounded-lg">
							<h3 className="text-sm font-medium text-gray-700 mb-2">Current Configuration</h3>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
								<div>
									<strong>API Base URL:</strong> {apiConfig.baseUrl}
								</div>
								<div>
									<strong>Timeout:</strong> {apiConfig.timeout}ms
								</div>
								<div>
									<strong>Cache Size:</strong> {cacheInfo.size} entries
								</div>
								<div>
									<strong>Sessions Loaded:</strong> {metadata.total}
								</div>
							</div>
						</div>
					)}
				</div>

				{/* Action Buttons */}
				<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
					<div className="flex flex-wrap gap-4">
						<Button
							onClick={handleLoadSessions}
							disabled={loading || !organizationId || !tenantId}
							className="flex items-center space-x-2"
						>
							{loading ? (
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
							) : null}
							<span>Load Sessions</span>
						</Button>

						<Button
							onClick={handleStoreSession}
							disabled={loading || !organizationId || !tenantId}
							variant="outline"
						>
							Store Sample Session
						</Button>

						<Button
							onClick={refetch}
							disabled={loading}
							variant="outline"
						>
							Refresh
						</Button>

						<Button
							onClick={clearCache}
							variant="outline"
						>
							Clear Cache
						</Button>
					</div>

					{/* Progress bar */}
					{loading && (
						<div className="mt-4">
							<div className="flex justify-between text-sm text-gray-600 mb-1">
								<span>Loading...</span>
								<span>{Math.round(progress)}%</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-2">
								<div
									className="bg-blue-600 h-2 rounded-full transition-all duration-300"
									style={{ width: `${progress}%` }}
								/>
							</div>
						</div>
					)}

					{/* Error display */}
					{error && (
						<div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
							<div className="flex justify-between items-start">
								<div>
									<h3 className="text-sm font-medium text-red-800">API Error</h3>
									<p className="text-sm text-red-600 mt-1">{error}</p>
								</div>
								<Button
									onClick={refetch}
									variant="outline"
									size="sm"
									className="text-red-600 border-red-300"
								>
									Retry
								</Button>
							</div>
						</div>
					)}
				</div>

				{/* Tabs */}
				<div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
					<div className="border-b border-gray-200">
						<nav className="flex space-x-8 px-6">
							<button
								onClick={() => setActiveTab('sessions')}
								className={`py-4 px-1 border-b-2 font-medium text-sm ${
									activeTab === 'sessions'
										? 'border-blue-500 text-blue-600'
										: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
								}`}
							>
								Sessions ({sessions.length})
							</button>
							<button
								onClick={() => setActiveTab('replay')}
								className={`py-4 px-1 border-b-2 font-medium text-sm ${
									activeTab === 'replay'
										? 'border-blue-500 text-blue-600'
										: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
								}`}
								disabled={!hasData}
							>
								Replay {selectedSessionId && `(${selectedSessionId})`}
							</button>
						</nav>
					</div>

					{/* Tab Content */}
					<div className="p-6">
						{activeTab === 'sessions' && (
							<div>
								{hasSessions ? (
									<div className="space-y-4">
										<h3 className="text-lg font-medium">Available Sessions</h3>
										<div className="grid gap-4">
											{sessions.map((session, index) => (
												<div
													key={index}
													className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
													onClick={() => handleSessionSelect(session)}
												>
													<div className="flex justify-between items-start">
														<div>
															<div className="font-medium text-gray-900">
																Session {index + 1}
															</div>
															<div className="text-sm text-gray-600 mt-1">
																Employee: {session.employeeId}
															</div>
															<div className="text-sm text-gray-600">
																Timestamp: {new Date(session.timestamp).toLocaleString()}
															</div>
														</div>
														<Button size="sm" variant="outline">
															Load Replay
														</Button>
													</div>
												</div>
											))}
										</div>
									</div>
								) : (
									<div className="text-center py-12">
										<div className="text-gray-400 mb-4">
											<svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
											</svg>
										</div>
										<h3 className="text-lg font-medium text-gray-900 mb-2">No Sessions Found</h3>
										<p className="text-gray-600 mb-4">
											Load sessions using the API configuration above, or store a sample session to get started.
										</p>
									</div>
								)}
							</div>
						)}

						{activeTab === 'replay' && (
							<div>
								{hasData ? (
									<div style={{ height: 'calc(100vh - 500px)', minHeight: '600px' }}>
										<ClarityReplayEnhanced
											sessionId={selectedSessionId}
											organizationId={organizationId}
											tenantId={tenantId}
											employeeId={employeeId}
											decodedPayloads={decodedData}
											autoPlay={false}
											showAdvancedControls={true}
											enableFullscreen={true}
											className="h-full"
											onReady={() => console.log('Replay ready')}
											onError={(error) => console.error('Replay error:', error)}
										/>
									</div>
								) : (
									<div className="text-center py-12">
										<div className="text-gray-400 mb-4">
											<svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
												<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
											</svg>
										</div>
										<h3 className="text-lg font-medium text-gray-900 mb-2">No Session Selected</h3>
										<p className="text-gray-600">
											Select a session from the Sessions tab to view its replay.
										</p>
									</div>
								)}
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

export default ApiIntegrationExample;
