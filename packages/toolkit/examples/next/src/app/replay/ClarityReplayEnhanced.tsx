'use client';

import React, { useRef, useState, useCallback, useEffect } from 'react';
import { Data } from 'clarity-decode';
import ClarityReplayWrapper, { ClarityReplayRef } from './ClarityReplayWrapper';
import ClarityAdvancedControls from './components/ClarityAdvancedControls';
import { useClarityData } from './hooks/useClarityData';
import { useErrorHandler } from './hooks/useErrorHandler';

interface ClarityReplayEnhancedProps {
	sessionId?: string;
	decodedPayloads?: Data.DecodedPayload[];
	autoPlay?: boolean;
	showAdvancedControls?: boolean;
	enableFullscreen?: boolean;
	className?: string;
	onReady?: () => void;
	onError?: (error: Error) => void;
	onExport?: () => void;
}

/**
 * Enhanced Clarity replay component with all advanced features
 * Combines the optimized replay component with advanced controls and data management
 */
const ClarityReplayEnhanced: React.FC<ClarityReplayEnhancedProps> = ({
	sessionId,
	decodedPayloads: externalPayloads,
	autoPlay = false,
	showAdvancedControls = true,
	enableFullscreen = true,
	className = '',
	onReady,
	onError,
	onExport
}) => {
	const replayRef = useRef<ClarityReplayRef>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	
	// State management
	const [playbackState, setPlaybackState] = useState({
		isPlaying: false,
		currentTime: 0,
		duration: 0,
		playbackSpeed: 1,
		isFullscreen: false,
		isReady: false
	});

	// Data management (only if sessionId is provided)
	const {
		decodedData: loadedData,
		loading,
		error: dataError,
		progress,
		load,
		refetch
	} = useClarityData({
		sessionId,
		autoLoad: !!sessionId,
		enableCaching: true,
		retryAttempts: 3
	});

	// Error handling
	const { handleError, clearAllErrors } = useErrorHandler({
		maxRetries: 3,
		onError: (errorInfo) => {
			console.error('Enhanced replay error:', errorInfo);
			onError?.(errorInfo.error);
		}
	});

	// Use external payloads or loaded data
	const decodedPayloads = externalPayloads || loadedData;

	// Fullscreen handling
	const toggleFullscreen = useCallback(async () => {
		if (!enableFullscreen || !containerRef.current) return;

		try {
			if (!playbackState.isFullscreen) {
				await containerRef.current.requestFullscreen();
				setPlaybackState(prev => ({ ...prev, isFullscreen: true }));
			} else {
				await document.exitFullscreen();
				setPlaybackState(prev => ({ ...prev, isFullscreen: false }));
			}
		} catch (error) {
			console.warn('Fullscreen not supported or failed:', error);
		}
	}, [enableFullscreen, playbackState.isFullscreen]);

	// Listen for fullscreen changes
	useEffect(() => {
		const handleFullscreenChange = () => {
			const isFullscreen = !!document.fullscreenElement;
			setPlaybackState(prev => ({ ...prev, isFullscreen }));
		};

		document.addEventListener('fullscreenchange', handleFullscreenChange);
		return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
	}, []);

	// Keyboard shortcuts
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (!playbackState.isReady) return;

			switch (e.code) {
				case 'Space':
					e.preventDefault();
					if (playbackState.isPlaying) {
						handlePause();
					} else {
						handlePlay();
					}
					break;
				case 'ArrowLeft':
					e.preventDefault();
					handleSkip(-10);
					break;
				case 'ArrowRight':
					e.preventDefault();
					handleSkip(10);
					break;
				case 'KeyR':
					if (e.ctrlKey || e.metaKey) {
						e.preventDefault();
						handleReset();
					}
					break;
				case 'KeyF':
					if (enableFullscreen) {
						e.preventDefault();
						toggleFullscreen();
					}
					break;
			}
		};

		document.addEventListener('keydown', handleKeyDown);
		return () => document.removeEventListener('keydown', handleKeyDown);
	}, [playbackState.isReady, playbackState.isPlaying, enableFullscreen]);

	// Control handlers
	const handlePlay = useCallback(() => {
		replayRef.current?.play();
		setPlaybackState(prev => ({ ...prev, isPlaying: true }));
	}, []);

	const handlePause = useCallback(() => {
		replayRef.current?.pause();
		setPlaybackState(prev => ({ ...prev, isPlaying: false }));
	}, []);

	const handleReset = useCallback(() => {
		replayRef.current?.reset();
		setPlaybackState(prev => ({ ...prev, isPlaying: false, currentTime: 0 }));
	}, []);

	const handleSeek = useCallback((time: number) => {
		replayRef.current?.seek(time);
		setPlaybackState(prev => ({ ...prev, currentTime: time }));
	}, []);

	const handleSkip = useCallback((seconds: number) => {
		const newTime = Math.max(0, Math.min(playbackState.duration, playbackState.currentTime + seconds * 1000));
		handleSeek(newTime);
	}, [playbackState.currentTime, playbackState.duration, handleSeek]);

	const handleSpeedChange = useCallback((speed: number) => {
		setPlaybackState(prev => ({ ...prev, playbackSpeed: speed }));
	}, []);

	// Replay event handlers
	const handleReplayReady = useCallback(() => {
		const duration = replayRef.current?.getDuration() || 0;
		setPlaybackState(prev => ({ 
			...prev, 
			isReady: true, 
			duration,
			currentTime: 0 
		}));
		onReady?.();
	}, [onReady]);

	const handleReplayError = useCallback(async (error: Error) => {
		await handleError(error, 'replay');
	}, [handleError]);

	const handleTimeUpdate = useCallback((currentTime: number) => {
		setPlaybackState(prev => ({ ...prev, currentTime }));
	}, []);

	const handlePlayStateChange = useCallback((isPlaying: boolean) => {
		setPlaybackState(prev => ({ ...prev, isPlaying }));
	}, []);

	// Loading state
	if (loading) {
		return (
			<div className={`flex items-center justify-center h-96 bg-gray-50 rounded-lg border border-gray-200 ${className}`}>
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<p className="text-gray-600">Loading session data...</p>
					<div className="w-64 bg-gray-200 rounded-full h-2 mt-4">
						<div
							className="bg-blue-600 h-2 rounded-full transition-all duration-300"
							style={{ width: `${progress}%` }}
						/>
					</div>
					<p className="text-xs text-gray-500 mt-2">{Math.round(progress)}% complete</p>
				</div>
			</div>
		);
	}

	// Error state
	if (dataError) {
		return (
			<div className={`flex flex-col items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
				<div className="text-center max-w-md">
					<h3 className="text-lg font-semibold text-red-800 mb-2">Failed to Load Session</h3>
					<p className="text-red-600 mb-4 text-sm">{dataError}</p>
					<div className="space-x-2">
						<button
							onClick={refetch}
							className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
						>
							Retry
						</button>
						<button
							onClick={clearAllErrors}
							className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
						>
							Clear
						</button>
					</div>
				</div>
			</div>
		);
	}

	// No data state
	if (!decodedPayloads || decodedPayloads.length === 0) {
		return (
			<div className={`flex flex-col items-center justify-center h-96 bg-gray-50 border border-gray-200 rounded-lg p-6 ${className}`}>
				<div className="text-center">
					<div className="text-gray-400 mb-4">
						<svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
						</svg>
					</div>
					<h3 className="text-lg font-medium text-gray-900 mb-2">No Session Data</h3>
					<p className="text-gray-600">Provide session data or a session ID to view the replay.</p>
				</div>
			</div>
		);
	}

	return (
		<div
			ref={containerRef}
			className={`flex flex-col bg-white rounded-lg shadow-lg overflow-hidden ${
				playbackState.isFullscreen ? 'fixed inset-0 z-50' : ''
			} ${className}`}
		>
			{/* Header */}
			<div className="flex items-center justify-between px-4 py-3 bg-gray-50 border-b border-gray-200">
				<div className="flex items-center space-x-2">
					<div className="w-3 h-3 bg-red-500 rounded-full"></div>
					<div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
					<div className="w-3 h-3 bg-green-500 rounded-full"></div>
					<span className="ml-4 text-sm font-medium text-gray-700">
						Session Replay {sessionId && `- ${sessionId}`}
					</span>
				</div>
				
				{playbackState.isReady && (
					<div className="text-xs text-gray-500">
						{decodedPayloads.length} payloads • {Math.round(playbackState.duration / 1000)}s duration
					</div>
				)}
			</div>

			{/* Replay Container */}
			<div className="flex-1 relative bg-gray-100">
				<ClarityReplayWrapper
					ref={replayRef}
					decodedPayloads={decodedPayloads}
					autoPlay={autoPlay}
					playbackSpeed={playbackState.playbackSpeed}
					onReady={handleReplayReady}
					onError={handleReplayError}
					onTimeUpdate={handleTimeUpdate}
					onPlayStateChange={handlePlayStateChange}
					className="h-full"
					enableErrorBoundary={true}
				/>
			</div>

			{/* Advanced Controls */}
			{showAdvancedControls && playbackState.isReady && (
				<ClarityAdvancedControls
					replayRef={replayRef}
					decodedData={decodedPayloads}
					isPlaying={playbackState.isPlaying}
					currentTime={playbackState.currentTime}
					duration={playbackState.duration}
					playbackSpeed={playbackState.playbackSpeed}
					isFullscreen={playbackState.isFullscreen}
					onPlay={handlePlay}
					onPause={handlePause}
					onReset={handleReset}
					onSeek={handleSeek}
					onSpeedChange={handleSpeedChange}
					onFullscreenToggle={toggleFullscreen}
					onExport={onExport}
				/>
			)}
		</div>
	);
};

export default ClarityReplayEnhanced;
