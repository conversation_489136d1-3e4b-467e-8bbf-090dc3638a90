'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { decode } from 'clarity-decode';
import { Data } from 'clarity-decode';

interface UseClarityDataOptions {
	sessionId?: string;
	autoLoad?: boolean;
	enableCaching?: boolean;
	retryAttempts?: number;
	retryDelay?: number;
}

interface ClarityDataState {
	decodedData: Data.DecodedPayload[];
	loading: boolean;
	error: string | null;
	progress: number;
	sessionId: string | null;
}

/**
 * Custom hook for managing Clarity data in Next.js applications
 * Provides loading, caching, error handling, and retry logic
 */
export const useClarityData = (options: UseClarityDataOptions = {}) => {
	const {
		sessionId,
		autoLoad = false,
		enableCaching = true,
		retryAttempts = 3,
		retryDelay = 1000
	} = options;

	const [state, setState] = useState<ClarityDataState>({
		decodedData: [],
		loading: false,
		error: null,
		progress: 0,
		sessionId: null
	});

	// Cache for decoded data
	const cacheRef = useRef(new Map<string, Data.DecodedPayload[]>());
	const abortControllerRef = useRef<AbortController | null>(null);
	const retryTimeoutRef = useRef<NodeJS.Timeout>();

	// Load data from API with progress tracking
	const loadData = useCallback(async (targetSessionId: string, attempt = 0): Promise<boolean> => {
		// Check cache first
		if (enableCaching && cacheRef.current.has(targetSessionId)) {
			const cachedData = cacheRef.current.get(targetSessionId)!;
			setState(prev => ({
				...prev,
				decodedData: cachedData,
				loading: false,
				error: null,
				progress: 100,
				sessionId: targetSessionId
			}));
			return true;
		}

		// Cancel previous request
		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
		}

		// Create new abort controller
		abortControllerRef.current = new AbortController();

		setState(prev => ({
			...prev,
			loading: true,
			error: null,
			progress: 0,
			sessionId: targetSessionId
		}));

		try {
			// Fetch encoded data from API
			const response = await fetch(`/api/clarity-data?sessionId=${targetSessionId}`, {
				signal: abortControllerRef.current.signal,
				headers: {
					'Content-Type': 'application/json',
				},
			});

			if (!response.ok) {
				throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
			}

			// Update progress
			setState(prev => ({ ...prev, progress: 25 }));

			const encodedPayloads = await response.json();

			if (!Array.isArray(encodedPayloads) || encodedPayloads.length === 0) {
				throw new Error('No session data available');
			}

			// Update progress
			setState(prev => ({ ...prev, progress: 50 }));

			// Decode payloads with progress tracking
			const decodedPayloads: Data.DecodedPayload[] = [];
			const totalPayloads = encodedPayloads.length;

			for (let i = 0; i < totalPayloads; i++) {
				try {
					const decoded = decode(encodedPayloads[i]);
					decodedPayloads.push(decoded);
					
					// Update progress
					const progress = 50 + ((i + 1) / totalPayloads) * 50;
					setState(prev => ({ ...prev, progress }));
				} catch (decodeError) {
					console.warn(`Failed to decode payload ${i}:`, decodeError);
					// Continue with other payloads
				}
			}

			if (decodedPayloads.length === 0) {
				throw new Error('Failed to decode any session data');
			}

			// Cache the decoded data
			if (enableCaching) {
				cacheRef.current.set(targetSessionId, decodedPayloads);
			}

			setState(prev => ({
				...prev,
				decodedData: decodedPayloads,
				loading: false,
				error: null,
				progress: 100,
				sessionId: targetSessionId
			}));

			return true;

		} catch (error) {
			// Handle abort
			if (error instanceof Error && error.name === 'AbortError') {
				return false;
			}

			const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

			// Retry logic
			if (attempt < retryAttempts) {
				console.warn(`Attempt ${attempt + 1} failed, retrying in ${retryDelay}ms:`, errorMessage);
				
				return new Promise((resolve) => {
					retryTimeoutRef.current = setTimeout(async () => {
						const success = await loadData(targetSessionId, attempt + 1);
						resolve(success);
					}, retryDelay * (attempt + 1)); // Exponential backoff
				});
			}

			// Final failure
			setState(prev => ({
				...prev,
				loading: false,
				error: errorMessage,
				progress: 0
			}));

			return false;
		}
	}, [enableCaching, retryAttempts, retryDelay]);

	// Auto-load effect
	useEffect(() => {
		if (autoLoad && sessionId) {
			loadData(sessionId);
		}
	}, [autoLoad, sessionId, loadData]);

	// Cleanup effect
	useEffect(() => {
		return () => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
			if (retryTimeoutRef.current) {
				clearTimeout(retryTimeoutRef.current);
			}
		};
	}, []);

	// Manual load function
	const load = useCallback((targetSessionId: string) => {
		return loadData(targetSessionId);
	}, [loadData]);

	// Refetch current session
	const refetch = useCallback(() => {
		if (state.sessionId) {
			// Clear cache for current session
			if (enableCaching) {
				cacheRef.current.delete(state.sessionId);
			}
			return loadData(state.sessionId);
		}
		return Promise.resolve(false);
	}, [state.sessionId, loadData, enableCaching]);

	// Clear cache
	const clearCache = useCallback((targetSessionId?: string) => {
		if (targetSessionId) {
			cacheRef.current.delete(targetSessionId);
		} else {
			cacheRef.current.clear();
		}
	}, []);

	// Cancel current request
	const cancel = useCallback(() => {
		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
		}
		if (retryTimeoutRef.current) {
			clearTimeout(retryTimeoutRef.current);
		}
		setState(prev => ({
			...prev,
			loading: false,
			progress: 0
		}));
	}, []);

	// Get cache info
	const getCacheInfo = useCallback(() => {
		return {
			size: cacheRef.current.size,
			keys: Array.from(cacheRef.current.keys())
		};
	}, []);

	return {
		...state,
		load,
		refetch,
		cancel,
		clearCache,
		getCacheInfo,
		isLoading: state.loading,
		hasData: state.decodedData.length > 0,
		hasError: !!state.error
	};
};
