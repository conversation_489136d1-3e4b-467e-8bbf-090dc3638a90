'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { decode } from 'clarity-decode';
import { Data } from 'clarity-decode';
import { trackingApiClient, isErrorResponse } from '../api/tracking-client';
import { IClocSession, IDecodedSessionData } from '../types/api-types';

interface UseClarityDataOptions {
	sessionId?: string;
	organizationId?: string;
	tenantId?: string;
	employeeId?: string;
	autoLoad?: boolean;
	enableCaching?: boolean;
	retryAttempts?: number;
	retryDelay?: number;
}

interface ClarityDataState {
	decodedData: Data.DecodedPayload[];
	sessions: IClocSession[];
	loading: boolean;
	error: string | null;
	progress: number;
	sessionId: string | null;
	metadata: {
		total: number;
		processed: number;
		failed: number;
	};
}

/**
 * Custom hook for managing Clarity data in Next.js applications
 * Provides loading, caching, error handling, and retry logic
 */
export const useClarityData = (options: UseClarityDataOptions = {}) => {
	const {
		sessionId,
		organizationId,
		tenantId,
		employeeId,
		autoLoad = false,
		enableCaching = true,
		retryAttempts = 3,
		retryDelay = 1000
	} = options;

	const [state, setState] = useState<ClarityDataState>({
		decodedData: [],
		sessions: [],
		loading: false,
		error: null,
		progress: 0,
		sessionId: null,
		metadata: {
			total: 0,
			processed: 0,
			failed: 0
		}
	});

	// Cache for decoded data and sessions
	const cacheRef = useRef(new Map<string, IDecodedSessionData[]>());
	const sessionCacheRef = useRef(new Map<string, IClocSession[]>());
	const abortControllerRef = useRef<AbortController | null>(null);
	const retryTimeoutRef = useRef<NodeJS.Timeout>();

	// Set API context when options change
	useEffect(() => {
		trackingApiClient.setContext(organizationId, tenantId);
	}, [organizationId, tenantId]);

	// Load sessions from the tracking API (cached)
	const loadSessions = useCallback(
		async (attempt = 0): Promise<boolean> => {
			// Create cache key based on context
			const cacheKey = `${organizationId || 'default'}-${tenantId || 'default'}-${employeeId || 'all'}`;

			// Check cache first
			if (enableCaching && sessionCacheRef.current.has(cacheKey)) {
				const cachedSessions = sessionCacheRef.current.get(cacheKey)!;
				setState((prev) => ({
					...prev,
					sessions: cachedSessions,
					loading: false,
					error: null,
					progress: 100,
					metadata: {
						...prev.metadata,
						total: cachedSessions.length
					}
				}));
				return true;
			}

			// Cancel previous request
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}

			// Create new abort controller
			abortControllerRef.current = new AbortController();

			setState((prev) => ({
				...prev,
				loading: true,
				error: null,
				progress: 0
			}));

			try {
				// Update progress
				setState((prev) => ({ ...prev, progress: 25 }));

				// Fetch sessions from tracking API
				const response = await trackingApiClient.getSessionsWithContext(organizationId, tenantId, employeeId);

				if (isErrorResponse(response)) {
					throw new Error(response.error);
				}

				const sessions = response.data;

				// Update progress
				setState((prev) => ({ ...prev, progress: 75 }));

				// Cache the sessions
				if (enableCaching) {
					sessionCacheRef.current.set(cacheKey, sessions);
				}

				setState((prev) => ({
					...prev,
					sessions,
					loading: false,
					error: null,
					progress: 100,
					metadata: {
						...prev.metadata,
						total: sessions.length
					}
				}));

				return true;
			} catch (error) {
				// Handle abort
				if (error instanceof Error && error.name === 'AbortError') {
					return false;
				}

				const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

				// Retry logic
				if (attempt < retryAttempts) {
					console.warn(`Attempt ${attempt + 1} failed, retrying in ${retryDelay}ms:`, errorMessage);

					return new Promise((resolve) => {
						retryTimeoutRef.current = setTimeout(
							async () => {
								const success = await loadSessions(attempt + 1);
								resolve(success);
							},
							retryDelay * (attempt + 1)
						); // Exponential backoff
					});
				}

				// Final failure
				setState((prev) => ({
					...prev,
					loading: false,
					error: errorMessage,
					progress: 0
				}));

				return false;
			}
		},
		[organizationId, tenantId, employeeId, enableCaching, retryAttempts, retryDelay]
	);

	// Load and decode specific session data
	const loadSessionData = useCallback(
		async (targetSessionId: string, attempt = 0): Promise<boolean> => {
			// Check cache first
			const cacheKey = `decoded-${targetSessionId}`;
			if (enableCaching && cacheRef.current.has(cacheKey)) {
				const cachedData = cacheRef.current.get(cacheKey)!;
				const decodedPayloads = cachedData
					.map((d) => d.decodedPayload)
					.filter(Boolean) as Data.DecodedPayload[];

				setState((prev) => ({
					...prev,
					decodedData: decodedPayloads,
					loading: false,
					error: null,
					progress: 100,
					sessionId: targetSessionId
				}));
				return true;
			}

			setState((prev) => ({
				...prev,
				loading: true,
				error: null,
				progress: 0,
				sessionId: targetSessionId
			}));

			try {
				// First, get all sessions to find the target session
				const sessionsResponse = await trackingApiClient.getSessionsWithContext(
					organizationId,
					tenantId,
					employeeId
				);

				if (isErrorResponse(sessionsResponse)) {
					throw new Error(sessionsResponse.error);
				}

				// Find the specific session
				const targetSession = sessionsResponse.data.find(
					(session) =>
						session.payload.includes(targetSessionId) ||
						session.timestamp === targetSessionId ||
						session.employeeId === targetSessionId
				);

				if (!targetSession) {
					throw new Error(`Session ${targetSessionId} not found`);
				}

				// Update progress
				setState((prev) => ({ ...prev, progress: 50 }));

				// Decode the session payload
				try {
					const decodedPayload = decode(targetSession.payload);
					const decodedData: IDecodedSessionData = {
						id: targetSessionId,
						session: targetSession,
						decodedPayload,
						events: [], // Will be populated by the visualizer
						duration: 0, // Will be calculated
						eventCount: 0, // Will be calculated
						metadata: {
							processedAt: new Date().toISOString(),
							version: 'api-integrated'
						}
					};

					// Cache the decoded data
					if (enableCaching) {
						cacheRef.current.set(cacheKey, [decodedData]);
					}

					setState((prev) => ({
						...prev,
						decodedData: [decodedPayload],
						loading: false,
						error: null,
						progress: 100,
						sessionId: targetSessionId,
						metadata: {
							...prev.metadata,
							processed: 1,
							failed: 0
						}
					}));

					return true;
				} catch (decodeError) {
					throw new Error(
						`Failed to decode session data: ${decodeError instanceof Error ? decodeError.message : 'Unknown decode error'}`
					);
				}
			} catch (error) {
				// Handle abort
				if (error instanceof Error && error.name === 'AbortError') {
					return false;
				}

				const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

				// Retry logic
				if (attempt < retryAttempts) {
					console.warn(`Attempt ${attempt + 1} failed, retrying in ${retryDelay}ms:`, errorMessage);

					return new Promise((resolve) => {
						retryTimeoutRef.current = setTimeout(
							async () => {
								const success = await loadSessionData(targetSessionId, attempt + 1);
								resolve(success);
							},
							retryDelay * (attempt + 1)
						); // Exponential backoff
					});
				}

				// Final failure
				setState((prev) => ({
					...prev,
					loading: false,
					error: errorMessage,
					progress: 0,
					metadata: {
						...prev.metadata,
						failed: prev.metadata.failed + 1
					}
				}));

				return false;
			}
		},
		[organizationId, tenantId, employeeId, enableCaching, retryAttempts, retryDelay]
	);

	// Auto-load effect
	useEffect(() => {
		if (autoLoad && sessionId) {
			loadSessionData(sessionId);
		} else if (autoLoad && !sessionId) {
			loadSessions();
		}
	}, [autoLoad, sessionId, loadSessionData, loadSessions]);

	// Cleanup effect
	useEffect(() => {
		return () => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
			if (retryTimeoutRef.current) {
				clearTimeout(retryTimeoutRef.current);
			}
		};
	}, []);

	// Manual load function - loads specific session data
	const load = useCallback(
		(targetSessionId: string) => {
			return loadSessionData(targetSessionId);
		},
		[loadSessionData]
	);

	// Load all sessions
	const loadAllSessions = useCallback(() => {
		return loadSessions();
	}, [loadSessions]);

	// Refetch current session
	const refetch = useCallback(() => {
		if (state.sessionId) {
			// Clear cache for current session
			if (enableCaching) {
				const cacheKey = `decoded-${state.sessionId}`;
				cacheRef.current.delete(cacheKey);
			}
			return loadSessionData(state.sessionId);
		}
		return Promise.resolve(false);
	}, [state.sessionId, loadSessionData, enableCaching]);

	// Clear cache
	const clearCache = useCallback((targetSessionId?: string) => {
		if (targetSessionId) {
			cacheRef.current.delete(targetSessionId);
		} else {
			cacheRef.current.clear();
		}
	}, []);

	// Cancel current request
	const cancel = useCallback(() => {
		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
		}
		if (retryTimeoutRef.current) {
			clearTimeout(retryTimeoutRef.current);
		}
		setState((prev) => ({
			...prev,
			loading: false,
			progress: 0
		}));
	}, []);

	// Get cache info
	const getCacheInfo = useCallback(() => {
		return {
			size: cacheRef.current.size,
			keys: Array.from(cacheRef.current.keys())
		};
	}, []);

	return {
		...state,
		// Data loading functions
		load, // Load specific session data
		loadAllSessions, // Load all sessions
		refetch, // Refetch current session

		// Control functions
		cancel,
		clearCache,
		getCacheInfo,

		// Computed properties
		isLoading: state.loading,
		hasData: state.decodedData.length > 0,
		hasSessions: state.sessions.length > 0,
		hasError: !!state.error,

		// API client access
		apiClient: trackingApiClient
	};
};
