'use client';

import { useState, useCallback, useRef } from 'react';

export interface ErrorInfo {
	error: Error;
	timestamp: number;
	context?: string;
	retryCount: number;
	recoverable: boolean;
}

interface UseErrorHandlerOptions {
	maxRetries?: number;
	retryDelay?: number;
	onError?: (errorInfo: ErrorInfo) => void;
	enableLogging?: boolean;
}

/**
 * Custom hook for comprehensive error handling in Clarity replay components
 * Provides retry logic, error categorization, and recovery mechanisms
 */
export const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {
	const {
		maxRetries = 3,
		retryDelay = 1000,
		onError,
		enableLogging = true
	} = options;

	const [errors, setErrors] = useState<ErrorInfo[]>([]);
	const [isRetrying, setIsRetrying] = useState(false);
	const retryTimeoutRef = useRef<NodeJS.Timeout>();

	// Error categorization
	const categorizeError = useCallback((error: Error): { recoverable: boolean; context: string } => {
		const message = error.message.toLowerCase();
		
		// Network/loading errors - usually recoverable
		if (message.includes('network') || message.includes('fetch') || message.includes('load')) {
			return { recoverable: true, context: 'network' };
		}
		
		// Visualizer setup errors - sometimes recoverable
		if (message.includes('visualizer') || message.includes('setup') || message.includes('iframe')) {
			return { recoverable: true, context: 'visualizer' };
		}
		
		// Data/parsing errors - usually not recoverable
		if (message.includes('parse') || message.includes('decode') || message.includes('invalid')) {
			return { recoverable: false, context: 'data' };
		}
		
		// Memory/performance errors - sometimes recoverable
		if (message.includes('memory') || message.includes('heap') || message.includes('performance')) {
			return { recoverable: true, context: 'memory' };
		}
		
		// Default to recoverable for unknown errors
		return { recoverable: true, context: 'unknown' };
	}, []);

	// Handle error with retry logic
	const handleError = useCallback(async (
		error: Error, 
		context?: string,
		retryFn?: () => Promise<void> | void
	): Promise<boolean> => {
		const { recoverable, context: errorContext } = categorizeError(error);
		const finalContext = context || errorContext;
		
		// Find existing error for retry counting
		const existingError = errors.find(e => 
			e.error.message === error.message && e.context === finalContext
		);
		const retryCount = existingError ? existingError.retryCount + 1 : 0;

		const errorInfo: ErrorInfo = {
			error,
			timestamp: Date.now(),
			context: finalContext,
			retryCount,
			recoverable
		};

		// Update errors list
		setErrors(prev => {
			const filtered = prev.filter(e => 
				!(e.error.message === error.message && e.context === finalContext)
			);
			return [...filtered, errorInfo];
		});

		// Log error if enabled
		if (enableLogging) {
			console.error(`Clarity Error [${finalContext}]:`, error, {
				retryCount,
				recoverable,
				timestamp: new Date(errorInfo.timestamp).toISOString()
			});
		}

		// Call external error handler
		onError?.(errorInfo);

		// Attempt retry if recoverable and under retry limit
		if (recoverable && retryCount < maxRetries && retryFn) {
			setIsRetrying(true);
			
			return new Promise((resolve) => {
				retryTimeoutRef.current = setTimeout(async () => {
					try {
						await retryFn();
						setIsRetrying(false);
						
						// Remove error from list on successful retry
						setErrors(prev => prev.filter(e => e !== errorInfo));
						resolve(true);
					} catch (retryError) {
						setIsRetrying(false);
						// Recursively handle retry error
						const success = await handleError(
							retryError instanceof Error ? retryError : new Error('Retry failed'),
							finalContext,
							retryFn
						);
						resolve(success);
					}
				}, retryDelay * (retryCount + 1)); // Exponential backoff
			});
		}

		return false;
	}, [errors, categorizeError, enableLogging, onError, maxRetries, retryDelay]);

	// Clear specific error
	const clearError = useCallback((error: Error, context?: string) => {
		setErrors(prev => prev.filter(e => 
			!(e.error.message === error.message && (!context || e.context === context))
		));
	}, []);

	// Clear all errors
	const clearAllErrors = useCallback(() => {
		setErrors([]);
		setIsRetrying(false);
		if (retryTimeoutRef.current) {
			clearTimeout(retryTimeoutRef.current);
		}
	}, []);

	// Get errors by context
	const getErrorsByContext = useCallback((context: string) => {
		return errors.filter(e => e.context === context);
	}, [errors]);

	// Get latest error
	const getLatestError = useCallback(() => {
		return errors.length > 0 ? errors[errors.length - 1] : null;
	}, [errors]);

	// Check if has recoverable errors
	const hasRecoverableErrors = useCallback(() => {
		return errors.some(e => e.recoverable);
	}, [errors]);

	// Get error summary
	const getErrorSummary = useCallback(() => {
		const summary = {
			total: errors.length,
			recoverable: errors.filter(e => e.recoverable).length,
			byContext: {} as Record<string, number>
		};

		errors.forEach(e => {
			summary.byContext[e.context || 'unknown'] = (summary.byContext[e.context || 'unknown'] || 0) + 1;
		});

		return summary;
	}, [errors]);

	// Cleanup on unmount
	const cleanup = useCallback(() => {
		if (retryTimeoutRef.current) {
			clearTimeout(retryTimeoutRef.current);
		}
		setErrors([]);
		setIsRetrying(false);
	}, []);

	return {
		errors,
		isRetrying,
		handleError,
		clearError,
		clearAllErrors,
		getErrorsByContext,
		getLatestError,
		hasRecoverableErrors,
		getErrorSummary,
		cleanup
	};
};
