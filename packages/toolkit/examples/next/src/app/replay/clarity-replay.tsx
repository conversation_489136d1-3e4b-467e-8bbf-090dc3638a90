'use client';

import React, { useEffect, useRef, useState, useCallback, useImperativeHandle, forwardRef, useMemo } from 'react';
import { Data } from 'clarity-decode';
import { Visualizer } from 'clarity-visualize';
import { PauseIcon, PlayIcon, RotateCcw } from 'lucide-react';
import { Button, Input, ThemedButton } from '@cloc/ui';
import { useMemoryOptimizedReplay } from './hooks/useMemoryOptimizedReplay';
import { useErrorHandler } from './hooks/useErrorHandler';

// Enhanced TypeScript interfaces following integration guide patterns
export interface ClarityReplayProps {
	decodedPayloads: Data.DecodedPayload[];
	width?: number;
	height?: number;
	autoPlay?: boolean;
	playbackSpeed?: number;
	onReady?: () => void;
	onError?: (error: Error) => void;
	onTimeUpdate?: (currentTime: number) => void;
	onPlayStateChange?: (isPlaying: boolean) => void;
	className?: string;
}

export interface ClarityReplayRef {
	play: () => void;
	pause: () => void;
	seek: (time: number) => void;
	reset: () => void;
	getVisualizer: () => Visualizer | null;
	getCurrentTime: () => number;
	getDuration: () => number;
	isPlaying: () => boolean;
}

interface PlaybackState {
	isPlaying: boolean;
	hasLoaded: boolean;
	domReady: boolean;
	currentTime: number;
	duration: number;
	error: Error | null;
}

interface VisualizerSetupOptions {
	version: string;
	onresize: () => void;
	metadata?: any;
	mobile: boolean;
	vNext: boolean;
	locale: string;
	onclickMismatch?: () => void;
}

export const formatDuration = (ms: number) => {
	const totalSeconds = Math.floor(ms / 1000);
	const hours = Math.floor(totalSeconds / 3600);
	const minutes = Math.floor((totalSeconds % 3600) / 60);
	const seconds = totalSeconds % 60;
	return [
		hours > 0 ? String(hours).padStart(2, '0') : null,
		String(minutes).padStart(2, '0'),
		String(seconds).padStart(2, '0')
	]
		.filter(Boolean)
		.join(':');
};

const ClarityReplay = forwardRef<ClarityReplayRef, ClarityReplayProps>(
	(
		{
			decodedPayloads,
			width = 1920,
			height = 1080,
			autoPlay = false,
			playbackSpeed = 1,
			onReady,
			onError,
			onTimeUpdate,
			onPlayStateChange,
			className = ''
		},
		ref
	) => {
		// Enhanced hooks for performance and error handling
		const { processedData, clearCache, forceCleanup, getPerformanceMetrics } = useMemoryOptimizedReplay(
			decodedPayloads,
			{
				maxCacheSize: 50,
				cleanupInterval: 30000,
				enablePerformanceMonitoring: process.env.NODE_ENV === 'development'
			}
		);

		const { handleError, clearAllErrors, getLatestError, hasRecoverableErrors, isRetrying } = useErrorHandler({
			maxRetries: 3,
			retryDelay: 1000,
			onError: (errorInfo) => {
				console.error('Clarity replay error:', errorInfo);
				onError?.(errorInfo.error);
			},
			enableLogging: true
		});

		// Refs for component state
		const iframeRef = useRef<HTMLIFrameElement>(null);
		const visualizerRef = useRef<Visualizer | null>(null);
		const decodedPayloadsRef = useRef<Data.DecodedPayload[]>([]);
		const eventsRef = useRef<Data.DecodedEvent[]>([]);
		const frameRef = useRef<number | null>(null);
		const wasPlayingRef = useRef(false);

		// State management with proper typing
		const [playbackState, setPlaybackState] = useState<PlaybackState>({
			isPlaying: false,
			hasLoaded: false,
			domReady: false,
			currentTime: 0,
			duration: 0,
			error: null
		});

		// Memoized visualizer setup options
		const visualizerOptions = useMemo<VisualizerSetupOptions>(
			() => ({
				version: decodedPayloads[0]?.envelope?.version || 'dev',
				onresize: () => {},
				metadata: undefined,
				mobile: false,
				vNext: true,
				locale: 'en-us',
				onclickMismatch: () => {}
			}),
			[decodedPayloads]
		);

		// Enhanced initialization with error handling and performance optimization
		const initializeVisualizer = useCallback(async () => {
			if (!iframeRef.current?.contentWindow || !decodedPayloads.length) {
				return;
			}

			const retryInitialization = async () => {
				// Clear previous errors
				clearAllErrors();
				setPlaybackState((prev) => ({ ...prev, error: null }));

				if (!visualizerRef.current) {
					visualizerRef.current = new Visualizer();
				}
				const visualizer = visualizerRef.current;

				// Store decoded payloads
				decodedPayloadsRef.current = decodedPayloads;

				// Use processed data if available for better performance
				let eventsToUse: Data.DecodedEvent[];
				let calculatedDuration: number;

				if (processedData) {
					eventsToUse = processedData.events;
					calculatedDuration = processedData.duration;
				} else {
					// Fallback to manual processing
					const merged = visualizer.merge(decodedPayloads);
					eventsToUse = (merged.events as Data.DecodedEvent[]).sort((a, b) => a.time - b.time);
					calculatedDuration = eventsToUse.length > 0 ? eventsToUse[eventsToUse.length - 1].time : 0;
				}

				// Setup visualizer with enhanced error handling
				await visualizer.setup(iframeRef.current!.contentWindow!, visualizerOptions);

				// Store events
				eventsRef.current = [...eventsToUse];

				// Render initial DOM state
				const merged = visualizer.merge(decodedPayloads);
				if (merged.dom) {
					await visualizer.dom(merged.dom);
				}

				setPlaybackState((prev) => ({
					...prev,
					hasLoaded: true,
					domReady: true,
					duration: calculatedDuration,
					currentTime: 0
				}));

				onReady?.();
				onTimeUpdate?.(0);

				if (autoPlay) {
					handlePlay();
				}
			};

			try {
				await retryInitialization();
			} catch (error) {
				const errorObj = error instanceof Error ? error : new Error('Failed to initialize visualizer');

				// Use enhanced error handler with retry logic
				const recovered = await handleError(errorObj, 'initialization', retryInitialization);

				if (!recovered) {
					setPlaybackState((prev) => ({ ...prev, error: errorObj }));
				}
			}
		}, [
			decodedPayloads,
			visualizerOptions,
			onReady,
			onError,
			onTimeUpdate,
			autoPlay,
			processedData,
			handleError,
			clearAllErrors
		]);

		// Initialize when iframe loads
		useEffect(() => {
			const iframe = iframeRef.current;
			if (!iframe) return;

			const handleIframeLoad = () => {
				initializeVisualizer();
			};

			if (iframe.contentDocument?.readyState === 'complete') {
				initializeVisualizer();
			} else {
				iframe.onload = handleIframeLoad;
				// Force reload iframe to trigger onload
				iframe.srcdoc = '<!DOCTYPE html><html><head></head><body></body></html>';
			}

			return () => {
				if (frameRef.current) {
					cancelAnimationFrame(frameRef.current);
					frameRef.current = null;
				}
				// Enhanced cleanup
				forceCleanup();
				clearAllErrors();
			};
		}, [initializeVisualizer, forceCleanup, clearAllErrors]);

		// Enhanced playback loop with better error handling
		const replayLoop = useCallback(() => {
			if (!playbackState.isPlaying) return;
			const visualizer = visualizerRef.current;
			if (!visualizer || !playbackState.domReady) return;

			const events = eventsRef.current;
			// Execute only if there are events to render
			if (events.length > 0) {
				try {
					let event = events[0];
					let end = event.time + 16 / playbackSpeed; // Adjust frame time based on playback speed

					let index = 0;
					const missingFields: string[] = [];
					while (event && event.time < end) {
						if (event.time === undefined) {
							missingFields.push(`event at index ${index} is missing 'time'`);
						}
						event = events[++index];
					}

					if (missingFields.length > 0) {
						console.warn('Replay warning: Missing required fields in events:', missingFields.join('; '));
					}

					const toRender = events.splice(0, index);
					if (toRender.length > 0) {
						const newTime = toRender[toRender.length - 1].time;
						setPlaybackState((prev) => ({ ...prev, currentTime: newTime }));
						onTimeUpdate?.(newTime);
					}
					visualizer.render(toRender);
				} catch (error) {
					console.error('Playback error:', error);
					setPlaybackState((prev) => ({
						...prev,
						isPlaying: false,
						error: error instanceof Error ? error : new Error('Playback failed')
					}));
					onError?.(error instanceof Error ? error : new Error('Playback failed'));
					return;
				}
			}

			if (events.length > 0) {
				frameRef.current = requestAnimationFrame(replayLoop);
			} else {
				// Playback finished
				setPlaybackState((prev) => ({ ...prev, isPlaying: false }));
				onPlayStateChange?.(false);
			}
		}, [playbackState.isPlaying, playbackState.domReady, playbackSpeed, onTimeUpdate, onError, onPlayStateChange]);

		// Start/stop playback based on isPlaying state
		useEffect(() => {
			if (playbackState.isPlaying) {
				frameRef.current = requestAnimationFrame(replayLoop);
			} else if (frameRef.current) {
				cancelAnimationFrame(frameRef.current);
				frameRef.current = null;
			}
			return () => {
				if (frameRef.current) {
					cancelAnimationFrame(frameRef.current);
					frameRef.current = null;
				}
			};
		}, [playbackState.isPlaying, replayLoop]);

		// Enhanced seek function with error handling
		const seekTo = useCallback(
			(seekTime: number) => {
				if (!visualizerRef.current || !decodedPayloadsRef.current.length) {
					console.warn('Cannot seek: visualizer not ready');
					return;
				}

				try {
					const wasPlaying = playbackState.isPlaying;
					wasPlayingRef.current = wasPlaying;

					setPlaybackState((prev) => ({
						...prev,
						isPlaying: false,
						currentTime: seekTime
					}));
					onPlayStateChange?.(false);
					onTimeUpdate?.(seekTime);

					// Merge all payloads for a fresh DOM and events
					const merged = visualizerRef.current.merge(decodedPayloadsRef.current);
					const allEvents = (merged.events as Data.DecodedEvent[]).sort((a, b) => a.time - b.time);

					// Find events up to the seek time
					const eventsToRender = allEvents.filter((event) => event.time <= seekTime);
					const remainingEvents = allEvents.filter((event) => event.time > seekTime);

					// Reset and render up to seek point
					if (merged.dom) {
						visualizerRef.current.dom(merged.dom);
					}

					if (eventsToRender.length > 0) {
						visualizerRef.current.render(eventsToRender);
					}

					eventsRef.current = remainingEvents;

					setPlaybackState((prev) => ({ ...prev, domReady: true }));
				} catch (error) {
					console.error('Seek error:', error);
					const errorObj = error instanceof Error ? error : new Error('Seek failed');
					setPlaybackState((prev) => ({ ...prev, error: errorObj }));
					onError?.(errorObj);
				}
			},
			[playbackState.isPlaying, onPlayStateChange, onTimeUpdate, onError]
		);

		// Handle seek from input
		const handleSeek = useCallback(
			(e: React.ChangeEvent<HTMLInputElement>) => {
				const seekTime = Number(e.target.value);
				seekTo(seekTime);
			},
			[seekTo]
		);

		// Resume playback if it was playing before seek
		useEffect(() => {
			if (!playbackState.isPlaying && wasPlayingRef.current) {
				setPlaybackState((prev) => ({ ...prev, isPlaying: true }));
				onPlayStateChange?.(true);
				wasPlayingRef.current = false;
			}
		}, [playbackState.currentTime, playbackState.isPlaying, onPlayStateChange]);

		// Enhanced control functions
		const handlePlay = useCallback(() => {
			if (!playbackState.hasLoaded) return;
			setPlaybackState((prev) => ({ ...prev, isPlaying: true }));
			onPlayStateChange?.(true);
		}, [playbackState.hasLoaded, onPlayStateChange]);

		const handlePause = useCallback(() => {
			setPlaybackState((prev) => ({ ...prev, isPlaying: false }));
			onPlayStateChange?.(false);
		}, [onPlayStateChange]);

		const handleReset = useCallback(() => {
			if (!visualizerRef.current || !decodedPayloadsRef.current.length) return;

			try {
				setPlaybackState((prev) => ({ ...prev, isPlaying: false }));
				onPlayStateChange?.(false);

				// Small delay to ensure playback stops
				setTimeout(() => {
					if (!visualizerRef.current) return;

					const merged = visualizerRef.current.merge(decodedPayloadsRef.current);
					const sortedEvents = (merged.events as Data.DecodedEvent[]).sort((a, b) => a.time - b.time);
					eventsRef.current = [...sortedEvents];

					if (iframeRef.current && merged.dom) {
						visualizerRef.current.dom(merged.dom);
					}

					setPlaybackState((prev) => ({
						...prev,
						currentTime: 0,
						domReady: true,
						isPlaying: true
					}));
					onTimeUpdate?.(0);
					onPlayStateChange?.(true);
				}, 100);
			} catch (error) {
				console.error('Reset error:', error);
				const errorObj = error instanceof Error ? error : new Error('Reset failed');
				setPlaybackState((prev) => ({ ...prev, error: errorObj }));
				onError?.(errorObj);
			}
		}, [onPlayStateChange, onTimeUpdate, onError]);

		// Expose methods via ref
		useImperativeHandle(
			ref,
			() => ({
				play: handlePlay,
				pause: handlePause,
				seek: seekTo,
				reset: handleReset,
				getVisualizer: () => visualizerRef.current,
				getCurrentTime: () => playbackState.currentTime,
				getDuration: () => playbackState.duration,
				isPlaying: () => playbackState.isPlaying
			}),
			[
				handlePlay,
				handlePause,
				seekTo,
				handleReset,
				playbackState.currentTime,
				playbackState.duration,
				playbackState.isPlaying
			]
		);

		// Enhanced error display with retry information
		const latestError = getLatestError();
		if (playbackState.error || latestError) {
			const errorToShow = playbackState.error || latestError?.error;
			const canRetry = latestError?.recoverable && hasRecoverableErrors();

			return (
				<div
					className={`flex flex-col justify-center items-center bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}
				>
					<div className="text-red-600 text-center max-w-md">
						<div className="mb-4">
							<svg
								className="w-12 h-12 mx-auto mb-4 text-red-500"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
								/>
							</svg>
						</div>

						<h3 className="font-semibold mb-2">{isRetrying ? 'Retrying...' : 'Replay Error'}</h3>

						<p className="text-sm mb-4">
							{errorToShow?.message || 'An unexpected error occurred while loading the session replay.'}
						</p>

						{latestError && (
							<p className="text-xs text-red-500 mb-4">
								Attempt {latestError.retryCount + 1} • Context: {latestError.context}
							</p>
						)}

						<div className="space-y-2">
							{!isRetrying && (
								<Button
									onClick={() => {
										clearAllErrors();
										setPlaybackState((prev) => ({ ...prev, error: null }));
										initializeVisualizer();
									}}
									className="w-full bg-red-600 hover:bg-red-700 text-white"
									disabled={isRetrying}
								>
									{canRetry ? 'Retry' : 'Try Again'}
								</Button>
							)}

							{isRetrying && (
								<div className="flex items-center justify-center space-x-2">
									<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
									<span className="text-sm">Retrying...</span>
								</div>
							)}

							{process.env.NODE_ENV === 'development' && (
								<details className="mt-4 text-left">
									<summary className="cursor-pointer text-sm text-red-600 hover:text-red-800">
										Show Debug Info
									</summary>
									<div className="mt-2 text-xs bg-red-100 p-2 rounded space-y-2">
										<div>
											<strong>Performance:</strong>
											<pre className="mt-1 overflow-auto max-h-20">
												{JSON.stringify(getPerformanceMetrics(), null, 2)}
											</pre>
										</div>
										{errorToShow?.stack && (
											<div>
												<strong>Stack Trace:</strong>
												<pre className="mt-1 overflow-auto max-h-32">{errorToShow.stack}</pre>
											</div>
										)}
									</div>
								</details>
							)}
						</div>
					</div>
				</div>
			);
		}

		return (
			<div className={`flex flex-col justify-center items-center bg-black overflow-hidden ${className}`}>
				{/* <iframe
					ref={iframeRef}
					id="clarity-replay"
					title="Clarity Session Replay"
					className="w-[90%] h-[95vh] min-w-[70vw] border-none"
					style={{
						width: `${width}px`,
						height: `${height}px`,
						display: playbackState.hasLoaded ? 'block' : 'none',
						overflow: 'hidden'
					}}
					sandbox="allow-same-origin allow-scripts"
				/> */}

				{/* Loading state */}
				{!playbackState.hasLoaded && (
					<div className="absolute inset-0 flex items-center justify-center bg-gray-50">
						<div className="text-center">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
							<p className="text-gray-600">Loading session replay...</p>
						</div>
					</div>
				)}

				{/* Controls overlay */}
				<div className="w-full flex-col dark:bg-black/70 bg-white text-black dark:text-white px-6 py-4 flex gap-2 z-10">
					{/* Slider and time */}
					<Input
						type="range"
						min={0}
						max={playbackState.duration}
						value={playbackState.currentTime}
						onChange={handleSeek}
						className="w-full flex-1 h-2 accent-blue-500 bg-gradient-to-r from-blue-500 to-blue-300 rounded-lg outline-none disabled:opacity-50"
						disabled={!playbackState.hasLoaded || playbackState.duration === 0}
					/>

					{/* Controls row */}
					<div className="flex items-center justify-between gap-3 mt-1">
						<div className="flex gap-2 justify-center items-center">
							<ThemedButton
								onClick={playbackState.isPlaying ? handlePause : handlePlay}
								disabled={!playbackState.hasLoaded}
								className="p-3 text-white rounded-full transition-colors duration-200 focus:outline-none"
								title={playbackState.isPlaying ? 'Pause' : 'Play'}
							>
								{playbackState.isPlaying ? <PauseIcon size={15} /> : <PlayIcon size={15} />}
							</ThemedButton>
							<Button
								onClick={handleReset}
								disabled={!playbackState.hasLoaded}
								variant={'outline'}
								className="bg-transparent border-none rounded-full text-2xl p-3 transition hover:bg-black/10 disabled:opacity-50 disabled:cursor-not-allowed"
								title="Reset"
							>
								<RotateCcw size={20} />
							</Button>
						</div>
						<span className="min-w-[90px] text-sm text-shadow-sm">
							{formatDuration(playbackState.currentTime)} / {formatDuration(playbackState.duration)}
						</span>
					</div>
				</div>
			</div>
		);
	}
);

ClarityReplay.displayName = 'ClarityReplay';

export default ClarityReplay;
