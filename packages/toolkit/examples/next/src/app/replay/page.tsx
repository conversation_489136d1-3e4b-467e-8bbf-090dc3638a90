'use client';

import { useEffect } from 'react';
import { useState } from 'react';
import {
	Accordion,
	AccordionItem,
	AccordionTrigger,
	AccordionContent,
	Input,
	DatePicker,
	ThemedButton
} from '@cloc/ui';
import ClarityReplay, { formatDuration } from './clarity-replay';
import { Data, decode } from 'clarity-decode';
import { Building2, ListFilter, Users } from 'lucide-react';
import { ClocActiveEmployeeSelector, ClocActiveOrganizationSelector } from '@cloc/atoms';

interface ISession {
	payload: string;
	timestamp: string;
}

// Helper to format ms to hh:mm:ss

export default function ReplayPage() {
	const [sessionPayloads, setSessionPayloads] = useState<string[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [decodedPayloads, setDecodedPayloads] = useState<Data.DecodedPayload[]>([]);
	const [sessionsById, setSessionsById] = useState<Record<string, Data.DecodedPayload[]>>({});
	const [currentSessionDecodedPayloads, setCurrentSessionDecodedPayloads] = useState<Data.DecodedPayload[]>([]);

	const fetchSessions = async () => {
		try {
			const res = await fetch('http://localhost:3000/api/tracking');
			if (!res.ok) {
				throw new Error(`Failed to fetch sessions: ${res.statusText}`);
			}
			const json: {
				data: ISession[];
				success: boolean;
			} = await res.json();
			setSessionPayloads(json && json.data ? json.data.map((s) => s.payload) : []);
		} catch (err: unknown) {
			if (err && typeof err === 'object' && 'message' in err && typeof (err as any).message === 'string') {
				setError((err as Error).message);
			} else {
				setError('Unknown error');
			}
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchSessions();
	}, []);

	useEffect(() => {
		setDecodedPayloads(() => sessionPayloads.map(decode));
	}, [sessionPayloads]);

	useEffect(() => {
		const grouped: Record<string, Data.DecodedPayload[]> = {};
		for (const payload of decodedPayloads) {
			const sessionId = payload?.envelope?.sessionId || 'unknown';
			if (!grouped[sessionId]) {
				grouped[sessionId] = [];
			}
			grouped[sessionId].push(payload);
		}
		setSessionsById(grouped);
	}, [decodedPayloads]);

	return (
		<div className="rounded-xl  shadow-lg bg-white dark:bg-black">
			<div className=" w-full flex-col dark:bg-black/70 bg-white text-black dark:text-white  px-6 py-4 flex gap-2 z-10 ">
				<h1 className="text-2xl font-bold text-gray-900 dark:text-white">Replays</h1>

				<ThemedButton className="mb-3 w-fit min-w-20" onClick={fetchSessions} disabled={loading}>
					{loading ? 'Refreshing...' : 'Refresh Sessions'}
				</ThemedButton>

				<Accordion
					className="bg-white dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-3 "
					type="single"
					collapsible
				>
					<AccordionItem value="item-1">
						<AccordionTrigger className="py-0">
							<div className=" flex gap-2 justify-center items-center text-lg font-semibold text-gray-900 dark:text-white ">
								<ListFilter size={20} className="text-gray-400" />
								<h2>Filters</h2>
							</div>
						</AccordionTrigger>
						<AccordionContent className="p-0">
							<div className="grid grid-cols-1 md:grid-cols-3 gap-4 my-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
										{/* <Calendar className="inline h-4 w-4 mr-1" /> */}
										Date Range
									</label>

									<div className="flex gap-2 mb-2">
										<div className="flex gap-2">
											<DatePicker />
											<Input type="time" />
										</div>
									</div>
									{/* <div className="flex gap-2">
										<div className="flex gap-2">
											<DatePicker />
											<Input type="time" />
										</div>
									</div> */}
									{/* <ClocDateRangePicker size="default" date={{ from: new Date(), to: new Date() }} /> */}
								</div>

								{
									<div>
										<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
											<Building2 className="inline h-4 w-4 mr-1" />
											Organization
										</label>
										{/* <SearchableSelect
									value={filterState.organizationId}
									options={organizationOptions}
									onChange={handleOrganizationChange}
									placeholder="Select organization"
									aria-label="Select organization"
								/> */}
										<ClocActiveOrganizationSelector />
									</div>
								}

								{
									<div>
										<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
											<Users className="inline h-4 w-4 mr-1" />
											User
										</label>
										{/* <SearchableSelect
									value={filterState.userId}
									options={userOptions}
									onChange={handleUserChange}
									onSearch={handleUserSearch}
									loading={loading}
									placeholder="Select user"
									aria-label="Select user"
								/> */}
										<ClocActiveEmployeeSelector />
									</div>
								}
							</div>
							<ThemedButton className="">Apply filter</ThemedButton>
						</AccordionContent>
					</AccordionItem>
				</Accordion>

				<h1 className="text-2xl font-bold text-gray-900 dark:text-white">Sessions</h1>

				<div className="space-y-4 ">
					{Object.entries(sessionsById).map(([sessionId, payloads]) => {
						// Calculate total duration for this session
						const totalDuration = payloads.reduce(
							(sum, elt) => sum + (Number(elt.envelope.duration) || 0),
							0
						);

						return (
							<div
								key={sessionId}
								className={`border rounded-lg border-gray-200 dark:border-gray-700 p-3 w-fit cursor-pointer transition
									${
										currentSessionDecodedPayloads === payloads
											? 'bg-blue-100 dark:bg-blue-900 border-blue-400 dark:border-blue-500 ring-2 ring-blue-300 dark:ring-blue-700'
											: 'bg-gray-50 dark:bg-gray-900 hover:bg-blue-50 dark:hover:bg-blue-950'
									}`}
								onClick={() => setCurrentSessionDecodedPayloads(payloads)}
								title="Click to view this session"
							>
								<div className="font-semibold dark:text-blue-300">
									Session ID: <span className="font-mono text-gray-500">{sessionId}</span>
								</div>
								<div className="dark:text-blue-300">
									<span>Total Duration:</span>{' '}
									<span className="text-gray-500">{formatDuration(totalDuration)}</span>
								</div>
								<div className="dark:text-blue-300">
									<span>URL:</span>{' '}
									<span className="text-gray-500">{payloads[0] && payloads[0].envelope.url}</span>
								</div>
							</div>
						);
					})}

					{Object.keys(sessionsById).length === 0 && (
						<div className="text-gray-500 text-sm">No sessions found.</div>
					)}
				</div>
			</div>
			{loading && <div>Loading sessions...</div>}
			{error && <div className="text-red-500">Error: {error}</div>}
			{!loading && !error && <ClarityReplay decodedPayloads={currentSessionDecodedPayloads} />}
		</div>
	);
}
