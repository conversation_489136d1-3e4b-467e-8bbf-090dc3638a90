import { NextResponse } from 'next/server';

interface ISession {
	payload: string;
	timestamp: string;
}
// In-memory temp DB for session replays

type IClocSession = ISession & {
	employeeId: string;
	organizationId: string;
	tenantId: string;
};

const sessionDB: IClocSession[] = [];

// Add CORS headers to allow any origin and any method
function withCors(response: Response) {
	response.headers.set('Access-Control-Allow-Origin', '*');
	response.headers.set('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
	response.headers.set('Access-Control-Allow-Headers', '*');
	return response;
}

export async function OPTIONS() {
	const response = new Response(null, { status: 204 });
	return withCors(response);
}

export async function POST(request: Request) {
	try {
		const data: ISession = await request.json();
		if (!data?.payload) {
			return withCors(NextResponse.json({ success: false, error: 'Missing data payload' }, { status: 400 }));
		}

		const organizationId = request.headers.get('organization-id') || '';
		const tenantId = request.headers.get('tenant-id') || '';
		const employeeId = '6aa62a33-fd99-4077-8ca8-e292de176e60'; // Will get employeeId from Token

		// Save payload in sessionDB

		sessionDB.push({ ...data, employeeId, organizationId, tenantId });
		return withCors(
			NextResponse.json({
				success: true,
				data: { ...data, employeeId, organizationId, tenantId }
			})
		);
	} catch (error) {
		console.error('Error processing tracking events:', error);
		const message = error instanceof Error ? error.message : 'An unknown error occurred.';
		const errorStack = error instanceof Error ? error.stack : undefined;
		return withCors(
			NextResponse.json(
				{ error: 'Failed to process tracking events', details: message, errorStack },
				{ status: 500 }
			)
		);
	}
}

export async function GET() {
	return withCors(NextResponse.json({ success: true, data: sessionDB }));
}
